import torch
import numpy as np
import logging

logger = logging.getLogger(__name__)

def safe_from_numpy(numpy_array):
    """Safely convert a NumPy array to a PyTorch tensor, handling the NumPy initialization issue.
    
    This is a workaround for the 'RuntimeError: Numpy is not available' error that can occur
    with certain combinations of Python 3.12, NumPy 2.x, and PyTorch.
    
    Args:
        numpy_array: A NumPy array to convert to a PyTorch tensor
        
    Returns:
        A PyTorch tensor with the same data as the input NumPy array
    """
    try:
        # Try the standard conversion first
        return torch.from_numpy(numpy_array)
    except Exception as e:
        logger.warning(f"Using fallback NumPy to PyTorch conversion: {str(e)}")
        # Fallback: convert to list first, then to tensor
        try:
            # Convert to Python list first
            array_list = numpy_array.tolist()
            # Create tensor from list
            tensor = torch.tensor(array_list)
            
            # Try to preserve dtype if possible
            numpy_dtype = str(numpy_array.dtype)
            if 'float' in numpy_dtype:
                if '64' in numpy_dtype:
                    tensor = tensor.to(torch.float64)
                elif '32' in numpy_dtype:
                    tensor = tensor.to(torch.float32)
                else:
                    tensor = tensor.to(torch.float)
            elif 'int' in numpy_dtype:
                if '64' in numpy_dtype:
                    tensor = tensor.to(torch.int64)
                elif '32' in numpy_dtype:
                    tensor = tensor.to(torch.int32)
                elif '16' in numpy_dtype:
                    tensor = tensor.to(torch.int16)
                elif '8' in numpy_dtype:
                    tensor = tensor.to(torch.int8)
                else:
                    tensor = tensor.to(torch.int)
            elif 'bool' in numpy_dtype:
                tensor = tensor.to(torch.bool)
            return tensor
        except Exception as inner_e:
            logger.error(f"Inner conversion error: {inner_e}")
            # Last resort: try direct tensor creation with float conversion
            try:
                return torch.tensor(numpy_array.astype(float).tolist())
            except Exception as last_e:
                logger.error(f"Final conversion attempt failed: {last_e}")
                raise RuntimeError(f"Could not convert NumPy array to PyTorch tensor: {str(e)}")