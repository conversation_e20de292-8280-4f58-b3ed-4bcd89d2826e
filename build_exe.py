#!/usr/bin/env python3
"""
GrainSight Executable Builder
Creates a standalone .exe file for the GrainSight application using PyInstaller.
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def check_requirements():
    """Check if all required tools and files are available."""
    print("🔍 Checking requirements...")
    
    # Check if PyInstaller is installed
    try:
        import PyInstaller
        print(f"✅ PyInstaller {PyInstaller.__version__} found")
    except ImportError:
        print("❌ PyInstaller not found. Installing...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✅ PyInstaller installed")
    
    # Check if main script exists
    if not os.path.exists("grainsight.py"):
        print("❌ Main script 'grainsight.py' not found!")
        return False
    print("✅ Main script found")
    
    # Check if model file exists
    model_path = "src/grainsight_components/models/FastSAM-x.pt"
    if not os.path.exists(model_path):
        print(f"⚠️  Model file not found at {model_path}")
        print("   The application will prompt users to download it on first run.")
    else:
        print("✅ FastSAM model found")
    
    return True

def create_spec_file():
    """Create PyInstaller spec file with all necessary configurations."""
    print("📝 Creating PyInstaller spec file...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-
import sys
import os
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# Collect all data files and submodules
datas = []
hiddenimports = []

# Add source code as data files
datas += [('src', 'src')]

# Add model files if they exist
if os.path.exists('src/grainsight_components/models'):
    datas += [('src/grainsight_components/models', 'src/grainsight_components/models')]

# Add icon files and assets
if os.path.exists('src/grainsight_components/gui/assets'):
    datas += [('src/grainsight_components/gui/assets', 'src/grainsight_components/gui/assets')]

# Add any additional resource directories
if os.path.exists('icons'):
    datas += [('icons', 'icons')]

# Collect PyTorch data files
try:
    datas += collect_data_files('torch')
    datas += collect_data_files('torchvision')
except:
    pass

# Collect Ultralytics data files
try:
    datas += collect_data_files('ultralytics')
except:
    pass

# Collect timm data files
try:
    datas += collect_data_files('timm')
except:
    pass

# Collect PySide6 data files
try:
    datas += collect_data_files('PySide6')
except:
    pass

# Hidden imports for all the modules that might not be detected
hiddenimports += [
    # Core Python modules
    'logging',
    'json',
    'csv',
    'sqlite3',
    
    # Scientific computing
    'numpy',
    'pandas',
    'matplotlib',
    'matplotlib.backends.backend_qt5agg',
    'matplotlib.backends.backend_qtagg',
    'PIL',
    'PIL.Image',
    'PIL.ImageTk',
    'cv2',
    'scipy',
    'scipy.spatial',
    'scipy.spatial.distance',
    
    # PyTorch and related
    'torch',
    'torchvision',
    'torchvision.transforms',
    'timm',
    'timm.models',
    'timm.layers',
    
    # Ultralytics/YOLO
    'ultralytics',
    'ultralytics.models',
    'ultralytics.models.yolo',
    'ultralytics.utils',
    
    # PySide6/Qt
    'PySide6',
    'PySide6.QtCore',
    'PySide6.QtGui',
    'PySide6.QtWidgets',
    'PySide6.QtOpenGL',
    
    # Application modules
    'src',
    'src.grainsight_components',
    'src.grainsight_components.core',
    'src.grainsight_components.core.segmentation',
    'src.grainsight_components.core.analysis',
    'src.grainsight_components.core.patching',
    'src.grainsight_components.core.patch_refinement',
    'src.grainsight_components.core.image_utils',
    'src.grainsight_components.gui',
    'src.grainsight_components.gui.main_window',
    'src.grainsight_components.gui.workers',
    'src.grainsight_components.gui.widgets',
    'src.grainsight_components.gui.dialogs',
    'src.grainsight_components.gui.utils',
    'src.grainsight_components.mobile_sam',
    'src.grainsight_components.mobile_sam.predictor',
    'src.grainsight_components.mobile_sam.build_sam',
    'src.grainsight_components.mobile_sam.automatic_mask_generator',
]

# Try to collect all submodules automatically
try:
    hiddenimports += collect_submodules('src')
    hiddenimports += collect_submodules('ultralytics')
    hiddenimports += collect_submodules('timm')
except:
    pass

# Remove duplicates
hiddenimports = list(set(hiddenimports))

block_cipher = None

a = Analysis(
    ['grainsight.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # Exclude unnecessary modules to reduce size
        'tkinter',
        'test',
        'unittest',
        'pdb',
        'doctest',
        'difflib',
        'inspect',
        'pydoc',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='GrainSight',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # Set to True for debugging, False for release
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='src/grainsight_components/gui/assets/icons/grain_icon.ico' if os.path.exists('src/grainsight_components/gui/assets/icons/grain_icon.ico') else None,
)
'''
    
    with open("grainsight.spec", "w") as f:
        f.write(spec_content)
    
    print("✅ Spec file created: grainsight.spec")

def create_icon():
    """Create an icon file if it doesn't exist."""
    icon_dir = "src/grainsight_components/gui/assets/icons"
    icon_path = os.path.join(icon_dir, "grain_icon.ico")
    
    if not os.path.exists(icon_path):
        print("⚠️  No .ico icon found, creating a simple one...")
        os.makedirs(icon_dir, exist_ok=True)
        
        # Try to create a simple icon using PIL if available
        try:
            from PIL import Image, ImageDraw
            
            # Create a simple 32x32 icon
            img = Image.new('RGBA', (32, 32), (0, 0, 0, 0))
            draw = ImageDraw.Draw(img)
            
            # Draw a simple grain-like circle
            draw.ellipse([4, 4, 28, 28], fill=(139, 69, 19, 255), outline=(101, 67, 33, 255), width=2)
            draw.ellipse([10, 10, 22, 22], fill=(160, 82, 45, 255))
            
            # Save as ICO
            img.save(icon_path, format='ICO', sizes=[(32, 32)])
            print(f"✅ Created simple icon: {icon_path}")
            
        except ImportError:
            print("⚠️  PIL not available, skipping icon creation")
        except Exception as e:
            print(f"⚠️  Could not create icon: {e}")

def build_executable():
    """Build the executable using PyInstaller."""
    print("🔨 Building executable...")
    
    # Clean previous builds
    if os.path.exists("build"):
        shutil.rmtree("build")
        print("🧹 Cleaned build directory")
    
    if os.path.exists("dist"):
        shutil.rmtree("dist")
        print("🧹 Cleaned dist directory")
    
    # Run PyInstaller
    cmd = [sys.executable, "-m", "PyInstaller", "--clean", "grainsight.spec"]
    
    print(f"Running: {' '.join(cmd)}")
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode == 0:
        print("✅ Build completed successfully!")
        
        # Check if executable was created
        exe_path = "dist/GrainSight.exe"
        if os.path.exists(exe_path):
            size_mb = os.path.getsize(exe_path) / (1024 * 1024)
            print(f"📦 Executable created: {exe_path} ({size_mb:.1f} MB)")
            return True
        else:
            print("❌ Executable not found in expected location")
            return False
    else:
        print("❌ Build failed!")
        print("STDOUT:", result.stdout)
        print("STDERR:", result.stderr)
        return False

def create_installer_script():
    """Create a simple installer script."""
    print("📦 Creating installer script...")
    
    installer_content = '''@echo off
echo GrainSight Installation
echo =====================

set "INSTALL_DIR=%PROGRAMFILES%\\GrainSight"
set "DESKTOP_LINK=%USERPROFILE%\\Desktop\\GrainSight.lnk"
set "START_MENU_DIR=%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs"

echo.
echo Installing GrainSight to: %INSTALL_DIR%
echo.

REM Create installation directory
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

REM Copy executable
copy "GrainSight.exe" "%INSTALL_DIR%\\GrainSight.exe"

REM Create desktop shortcut
echo Creating desktop shortcut...
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%DESKTOP_LINK%'); $Shortcut.TargetPath = '%INSTALL_DIR%\\GrainSight.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'GrainSight - Grain Analysis Tool'; $Shortcut.Save()"

REM Create start menu shortcut
echo Creating start menu shortcut...
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%START_MENU_DIR%\\GrainSight.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\\GrainSight.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'GrainSight - Grain Analysis Tool'; $Shortcut.Save()"

echo.
echo Installation completed!
echo.
echo GrainSight has been installed to: %INSTALL_DIR%
echo Desktop shortcut created: %DESKTOP_LINK%
echo Start menu shortcut created in Programs folder
echo.
echo You can now run GrainSight from the desktop or start menu.
echo.
pause
'''
    
    with open("dist/install.bat", "w") as f:
        f.write(installer_content)
    
    print("✅ Installer script created: dist/install.bat")

def create_readme():
    """Create a README file for the distribution."""
    print("📄 Creating README...")
    
    readme_content = '''# GrainSight - Standalone Application

## Installation

### Option 1: Simple Installation (Recommended)
1. Run `install.bat` as Administrator
2. Follow the prompts to install GrainSight to your system
3. Use the desktop or start menu shortcuts to run the application

### Option 2: Portable Usage
1. Simply run `GrainSight.exe` directly from this folder
2. No installation required

## First Run

When you first run GrainSight, you may need to download the FastSAM model:

1. The application will prompt you if the model is missing
2. Download FastSAM-x.pt from: https://github.com/CASIA-IVA-Lab/FastSAM/releases
3. The application will guide you to place it in the correct location

## System Requirements

- Windows 10 or later (64-bit)
- At least 4GB RAM (8GB recommended)
- 2GB free disk space
- Graphics card with OpenGL support (for better performance)

## Usage

1. **Load Image**: Click "Load Image" to select your grain image
2. **Set Scale**: Use the scale tool to set the measurement scale
3. **Run Analysis**: Choose between FastSAM or MobileSAM and click "Run Segmentation"
4. **View Results**: Examine the segmentation results and grain measurements
5. **Export Data**: Save your results as CSV or images

## Troubleshooting

### Application won't start
- Make sure you have the latest Windows updates
- Try running as Administrator
- Check Windows Defender/antivirus isn't blocking the application

### Model loading errors
- Ensure the FastSAM-x.pt model file is in the correct location
- Check you have enough free disk space
- Restart the application

### Performance issues
- Close other applications to free up memory
- Use smaller images or enable patch processing for large images
- Consider using a computer with more RAM or a dedicated graphics card

## Support

For issues and questions, please check the project documentation or contact support.

## Version Information

This is a standalone build of GrainSight v5.0
Built with PyInstaller for Windows x64
'''
    
    with open("dist/README.txt", "w") as f:
        f.write(readme_content)
    
    print("✅ README created: dist/README.txt")

def main():
    """Main build process."""
    print("🚀 GrainSight Executable Builder")
    print("=" * 40)
    
    # Check requirements
    if not check_requirements():
        print("❌ Requirements check failed!")
        return False
    
    # Create icon
    create_icon()
    
    # Create spec file
    create_spec_file()
    
    # Build executable
    if not build_executable():
        print("❌ Build process failed!")
        return False
    
    # Create additional files
    create_installer_script()
    create_readme()
    
    print("\n🎉 Build process completed successfully!")
    print("\nFiles created:")
    print("- dist/GrainSight.exe (Main executable)")
    print("- dist/install.bat (Installation script)")
    print("- dist/README.txt (User documentation)")
    
    print("\n📋 Next steps:")
    print("1. Test the executable: dist/GrainSight.exe")
    print("2. For distribution, zip the entire 'dist' folder")
    print("3. Users can run install.bat for system installation")
    print("4. Or run GrainSight.exe directly for portable usage")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
