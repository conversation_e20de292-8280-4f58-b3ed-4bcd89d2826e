# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['grainsight.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('src', 'src'),
        ('src/grainsight_components/models', 'src/grainsight_components/models'),
        ('src/grainsight_components/gui/assets', 'src/grainsight_components/gui/assets'),
    ],
    hiddenimports=[
        # Core Python modules that might be missed
        'inspect',
        'importlib',
        'importlib.resources',
        'importlib.abc',
        'importlib.metadata',
        'pkg_resources',
        'logging',
        'json',
        'csv',
        'sqlite3',
        'collections',
        'collections.abc',
        'functools',
        'itertools',
        'operator',
        'types',
        'typing',
        'weakref',
        
        # Scientific computing
        'numpy',
        'numpy.core',
        'numpy.core._methods',
        'numpy.lib',
        'numpy.lib.format',
        'pandas',
        'matplotlib',
        'matplotlib.backends',
        'matplotlib.backends.backend_qt5agg',
        'matplotlib.backends.backend_qtagg',
        'PIL',
        'PIL.Image',
        'PIL.ImageTk',
        'cv2',
        'scipy',
        'scipy.spatial',
        'scipy.spatial.distance',
        
        # PyTorch
        'torch',
        'torch.nn',
        'torch.nn.functional',
        'torchvision',
        'torchvision.transforms',
        'torchvision.models',
        
        # Ultralytics/YOLO
        'ultralytics',
        'ultralytics.models',
        'ultralytics.models.yolo',
        'ultralytics.utils',
        'ultralytics.nn',
        
        # timm
        'timm',
        'timm.models',
        'timm.layers',
        
        # PySide6/Qt
        'PySide6',
        'PySide6.QtCore',
        'PySide6.QtGui',
        'PySide6.QtWidgets',
        'PySide6.QtOpenGL',
        'shiboken6',
        
        # Application modules
        'src',
        'src.grainsight_components',
        'src.grainsight_components.core',
        'src.grainsight_components.core.segmentation',
        'src.grainsight_components.core.analysis',
        'src.grainsight_components.core.patching',
        'src.grainsight_components.core.patch_refinement',
        'src.grainsight_components.core.image_utils',
        'src.grainsight_components.gui',
        'src.grainsight_components.gui.main_window',
        'src.grainsight_components.gui.workers',
        'src.grainsight_components.gui.widgets',
        'src.grainsight_components.gui.dialogs',
        'src.grainsight_components.gui.utils',
        'src.grainsight_components.mobile_sam',
        'src.grainsight_components.mobile_sam.predictor',
        'src.grainsight_components.mobile_sam.build_sam',
        'src.grainsight_components.mobile_sam.automatic_mask_generator',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'test',
        'unittest',
        'pdb',
        'doctest',
        'pydoc',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='GrainSight',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,  # Set to True for debugging
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='GrainSight'
)
