#!/usr/bin/env python3
"""
Simplified GrainSight Executable Builder
Creates a standalone .exe file for the GrainSight application using PyInstaller.
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def create_simple_spec():
    """Create a simplified PyInstaller spec file."""
    print("📝 Creating simplified PyInstaller spec file...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['grainsight.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('src', 'src'),
        ('src/grainsight_components/models', 'src/grainsight_components/models'),
        ('src/grainsight_components/gui/assets', 'src/grainsight_components/gui/assets'),
    ],
    hiddenimports=[
        # Core Python modules that might be missed
        'inspect',
        'importlib',
        'importlib.resources',
        'importlib.abc',
        'importlib.metadata',
        'pkg_resources',
        'logging',
        'json',
        'csv',
        'sqlite3',
        'collections',
        'collections.abc',
        'functools',
        'itertools',
        'operator',
        'types',
        'typing',
        'weakref',
        
        # Scientific computing
        'numpy',
        'numpy.core',
        'numpy.core._methods',
        'numpy.lib',
        'numpy.lib.format',
        'pandas',
        'matplotlib',
        'matplotlib.backends',
        'matplotlib.backends.backend_qt5agg',
        'matplotlib.backends.backend_qtagg',
        'PIL',
        'PIL.Image',
        'PIL.ImageTk',
        'cv2',
        'scipy',
        'scipy.spatial',
        'scipy.spatial.distance',
        
        # PyTorch
        'torch',
        'torch.nn',
        'torch.nn.functional',
        'torchvision',
        'torchvision.transforms',
        'torchvision.models',
        
        # Ultralytics/YOLO
        'ultralytics',
        'ultralytics.models',
        'ultralytics.models.yolo',
        'ultralytics.utils',
        'ultralytics.nn',
        
        # timm
        'timm',
        'timm.models',
        'timm.layers',
        
        # PySide6/Qt
        'PySide6',
        'PySide6.QtCore',
        'PySide6.QtGui',
        'PySide6.QtWidgets',
        'PySide6.QtOpenGL',
        'shiboken6',
        
        # Application modules
        'src',
        'src.grainsight_components',
        'src.grainsight_components.core',
        'src.grainsight_components.core.segmentation',
        'src.grainsight_components.core.analysis',
        'src.grainsight_components.core.patching',
        'src.grainsight_components.core.patch_refinement',
        'src.grainsight_components.core.image_utils',
        'src.grainsight_components.gui',
        'src.grainsight_components.gui.main_window',
        'src.grainsight_components.gui.workers',
        'src.grainsight_components.gui.widgets',
        'src.grainsight_components.gui.dialogs',
        'src.grainsight_components.gui.utils',
        'src.grainsight_components.mobile_sam',
        'src.grainsight_components.mobile_sam.predictor',
        'src.grainsight_components.mobile_sam.build_sam',
        'src.grainsight_components.mobile_sam.automatic_mask_generator',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'test',
        'unittest',
        'pdb',
        'doctest',
        'pydoc',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='GrainSight',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,  # Set to True for debugging
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='GrainSight'
)
'''
    
    with open("grainsight_simple.spec", "w") as f:
        f.write(spec_content)
    
    print("✅ Simplified spec file created: grainsight_simple.spec")

def build_executable():
    """Build the executable using PyInstaller."""
    print("🔨 Building executable...")
    
    # Clean previous builds
    if os.path.exists("build"):
        shutil.rmtree("build")
        print("🧹 Cleaned build directory")
    
    if os.path.exists("dist"):
        shutil.rmtree("dist")
        print("🧹 Cleaned dist directory")
    
    # Run PyInstaller with the simplified spec
    cmd = [sys.executable, "-m", "PyInstaller", "--clean", "grainsight_simple.spec"]
    
    print(f"Running: {' '.join(cmd)}")
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode == 0:
        print("✅ Build completed successfully!")
        
        # Check if executable was created
        exe_path = "dist/GrainSight/GrainSight.exe"
        if os.path.exists(exe_path):
            size_mb = os.path.getsize(exe_path) / (1024 * 1024)
            print(f"📦 Executable created: {exe_path} ({size_mb:.1f} MB)")
            
            # Copy to root of dist for easier access
            shutil.copy2(exe_path, "dist/GrainSight.exe")
            print("📦 Copied executable to dist/GrainSight.exe")
            
            return True
        else:
            print("❌ Executable not found in expected location")
            return False
    else:
        print("❌ Build failed!")
        print("STDOUT:", result.stdout)
        print("STDERR:", result.stderr)
        return False

def create_batch_launcher():
    """Create a batch file to launch the application."""
    print("📝 Creating batch launcher...")
    
    batch_content = '''@echo off
cd /d "%~dp0"
echo Starting GrainSight...
echo.

REM Check if we're in the GrainSight folder
if exist "GrainSight.exe" (
    echo Found GrainSight.exe, launching...
    start "" "GrainSight.exe"
) else if exist "GrainSight\\GrainSight.exe" (
    echo Found GrainSight.exe in subfolder, launching...
    start "" "GrainSight\\GrainSight.exe"
) else (
    echo Error: GrainSight.exe not found!
    echo Please make sure this batch file is in the same folder as GrainSight.exe
    pause
)
'''
    
    with open("dist/Launch_GrainSight.bat", "w") as f:
        f.write(batch_content)
    
    print("✅ Batch launcher created: dist/Launch_GrainSight.bat")

def main():
    """Main build process."""
    print("🚀 GrainSight Simplified Executable Builder")
    print("=" * 50)
    
    # Check if PyInstaller is available
    try:
        import PyInstaller
        print(f"✅ PyInstaller {PyInstaller.__version__} found")
    except ImportError:
        print("❌ PyInstaller not found. Please install it first:")
        print("   uv add pyinstaller")
        return False
    
    # Check if main script exists
    if not os.path.exists("grainsight.py"):
        print("❌ Main script 'grainsight.py' not found!")
        return False
    print("✅ Main script found")
    
    # Create simplified spec file
    create_simple_spec()
    
    # Build executable
    if not build_executable():
        print("❌ Build process failed!")
        return False
    
    # Create batch launcher
    create_batch_launcher()
    
    # Create simple README
    readme_content = '''# GrainSight Executable

## How to Run
1. Double-click "Launch_GrainSight.bat" to start the application
2. Or run "GrainSight.exe" directly

## First Time Setup
- The application may prompt you to download the FastSAM model on first run
- Follow the instructions in the application to download and place the model file

## Troubleshooting
- If the application doesn't start, try running GrainSight.exe from a command prompt to see error messages
- Make sure all files in this folder stay together
- Check that your antivirus isn't blocking the executable

## System Requirements
- Windows 10 or later (64-bit)
- At least 4GB RAM (8GB recommended)
- 2GB free disk space
'''
    
    with open("dist/README.txt", "w") as f:
        f.write(readme_content)
    
    print("\n🎉 Build process completed successfully!")
    print("\nFiles created in 'dist' folder:")
    print("- GrainSight.exe (Main executable)")
    print("- GrainSight/ (Application folder with all dependencies)")
    print("- Launch_GrainSight.bat (Easy launcher)")
    print("- README.txt (User instructions)")
    
    print("\n📋 Next steps:")
    print("1. Test the executable: dist/Launch_GrainSight.bat")
    print("2. For distribution, zip the entire 'dist' folder")
    print("3. Users can run Launch_GrainSight.bat to start the application")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
