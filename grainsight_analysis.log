2025-05-30 00:44:50,233 - matplotlib.font_manager - DEBUG - findfont: Matching Arial:style=normal:variant=normal:weight=normal:stretch=normal:size=10.0.
2025-05-30 00:44:50,233 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='D:\\Github\\grainsight_v5\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymReg.ttf', name='STIXSizeThreeSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,233 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='D:\\Github\\grainsight_v5\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Bold.ttf', name='DejaVu Serif', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,233 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='D:\\Github\\grainsight_v5\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmr10.ttf', name='cmr10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,233 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='D:\\Github\\grainsight_v5\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralItalic.ttf', name='STIXGeneral', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,233 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='D:\\Github\\grainsight_v5\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmmi10.ttf', name='cmmi10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,233 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='D:\\Github\\grainsight_v5\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono.ttf', name='DejaVu Sans Mono', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,233 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='D:\\Github\\grainsight_v5\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymReg.ttf', name='STIXSizeTwoSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,233 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='D:\\Github\\grainsight_v5\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymBol.ttf', name='STIXSizeTwoSym', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,233 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='D:\\Github\\grainsight_v5\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUni.ttf', name='STIXNonUnicode', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,233 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='D:\\Github\\grainsight_v5\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymReg.ttf', name='STIXSizeFourSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,233 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='D:\\Github\\grainsight_v5\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneral.ttf', name='STIXGeneral', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,233 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='D:\\Github\\grainsight_v5\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Bold.ttf', name='DejaVu Sans Mono', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,233 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='D:\\Github\\grainsight_v5\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif.ttf', name='DejaVu Serif', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,240 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='D:\\Github\\grainsight_v5\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmtt10.ttf', name='cmtt10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,240 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='D:\\Github\\grainsight_v5\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBol.ttf', name='STIXGeneral', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,240 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='D:\\Github\\grainsight_v5\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Bold.ttf', name='DejaVu Sans', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,240 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='D:\\Github\\grainsight_v5\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFiveSymReg.ttf', name='STIXSizeFiveSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,240 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='D:\\Github\\grainsight_v5\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerifDisplay.ttf', name='DejaVu Serif Display', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,240 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='D:\\Github\\grainsight_v5\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansDisplay.ttf', name='DejaVu Sans Display', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,240 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='D:\\Github\\grainsight_v5\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBolIta.ttf', name='STIXNonUnicode', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:50,242 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='D:\\Github\\grainsight_v5\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBolIta.ttf', name='STIXGeneral', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:50,242 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='D:\\Github\\grainsight_v5\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmsy10.ttf', name='cmsy10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,242 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='D:\\Github\\grainsight_v5\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmss10.ttf', name='cmss10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,242 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='D:\\Github\\grainsight_v5\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymBol.ttf', name='STIXSizeOneSym', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,242 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='D:\\Github\\grainsight_v5\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniIta.ttf', name='STIXNonUnicode', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,242 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='D:\\Github\\grainsight_v5\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-BoldItalic.ttf', name='DejaVu Serif', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:50,242 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='D:\\Github\\grainsight_v5\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans.ttf', name='DejaVu Sans', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,242 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='D:\\Github\\grainsight_v5\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Oblique.ttf', name='DejaVu Sans Mono', style='oblique', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,242 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='D:\\Github\\grainsight_v5\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymReg.ttf', name='STIXSizeOneSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,244 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='D:\\Github\\grainsight_v5\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmb10.ttf', name='cmb10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,244 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='D:\\Github\\grainsight_v5\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Oblique.ttf', name='DejaVu Sans', style='oblique', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,244 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='D:\\Github\\grainsight_v5\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmex10.ttf', name='cmex10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,244 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='D:\\Github\\grainsight_v5\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-BoldOblique.ttf', name='DejaVu Sans Mono', style='oblique', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:50,245 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='D:\\Github\\grainsight_v5\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBol.ttf', name='STIXNonUnicode', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,245 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='D:\\Github\\grainsight_v5\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymBol.ttf', name='STIXSizeFourSym', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,246 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='D:\\Github\\grainsight_v5\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymBol.ttf', name='STIXSizeThreeSym', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,247 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='D:\\Github\\grainsight_v5\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-BoldOblique.ttf', name='DejaVu Sans', style='oblique', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:50,247 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='D:\\Github\\grainsight_v5\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Italic.ttf', name='DejaVu Serif', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,247 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MyriadPro-BoldCondIt.otf', name='Myriad Pro', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 11.535
2025-05-30 00:44:50,248 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MyriadPro-It.otf', name='Myriad Pro', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,248 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MyriadPro-Bold.otf', name='Myriad Pro', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,248 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSansGeorgian-Regular.ttf', name='Noto Sans Georgian', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,248 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\l_10646.ttf', name='Lucida Sans Unicode', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,248 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MinionPro-Bold.otf', name='Minion Pro', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,249 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSerifArmenian-Bold.ttf', name='Noto Serif Armenian', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,249 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Nirmala.ttf', name='Nirmala UI', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,249 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GenBkBasB.ttf', name='Gentium Book Basic', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,249 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\verdana.ttf', name='Verdana', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,250 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\malgun.ttf', name='Malgun Gothic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,250 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeuil.ttf', name='Segoe UI', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-05-30 00:44:50,250 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SCHLBKI.TTF', name='Century Schoolbook', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,252 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LFAXI.TTF', name='Lucida Fax', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,252 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SourceSansPro-Black.ttf', name='Source Sans Pro', style='normal', variant='normal', weight=900, stretch='normal', size='scalable')) = 10.525
2025-05-30 00:44:50,252 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SourceCodePro-ExtraLight.ttf', name='Source Code Pro', style='normal', variant='normal', weight=200, stretch='normal', size='scalable')) = 10.24
2025-05-30 00:44:50,252 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSans-CondensedBoldItalic.ttf', name='Noto Sans', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 11.535
2025-05-30 00:44:50,253 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LetterGothicStd-Bold.otf', name='Letter Gothic Std', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,253 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\EmojiOneColor-SVGinOT.ttf', name='EmojiOne Color', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,253 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LeelawUI.ttf', name='Leelawadee UI', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,254 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MyriadPro-CondIt.otf', name='Myriad Pro', style='italic', variant='normal', weight=400, stretch='condensed', size='scalable')) = 11.25
2025-05-30 00:44:50,254 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\andlso.ttf', name='Andalus', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,254 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ebrimabd.ttf', name='Ebrima', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,255 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ACaslonPro-Italic.otf', name='Adobe Caslon Pro', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,255 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NachlieliCLM-LightOblique.otf', name='Nachlieli CLM', style='oblique', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-05-30 00:44:50,255 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MyriadPro-BoldCond.otf', name='Myriad Pro', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-05-30 00:44:50,255 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NuevaStd-CondItalic.otf', name='Nueva Std', style='italic', variant='normal', weight=400, stretch='condensed', size='scalable')) = 11.25
2025-05-30 00:44:50,256 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\DejaVuSans-Oblique.ttf', name='DejaVu Sans', style='oblique', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,256 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRAHV.TTF', name='Franklin Gothic Heavy', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,256 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\phagspa.ttf', name='Microsoft PhagsPa', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,256 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mvboli.ttf', name='MV Boli', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,256 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\AdobeMyungjoStd-Medium.otf', name='Adobe Myungjo Std', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-05-30 00:44:50,258 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ARIALNB.TTF', name='Arial', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 0.5349999999999999
2025-05-30 00:44:50,258 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MyriadArabic-Regular.otf', name='Myriad Arabic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,258 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GenBasI.ttf', name='Gentium Basic', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,259 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSerif-CondensedItalic.ttf', name='Noto Serif', style='italic', variant='normal', weight=400, stretch='condensed', size='scalable')) = 11.25
2025-05-30 00:44:50,259 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Caladea-Regular.ttf', name='Caladea', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,260 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ERASBD.TTF', name='Eras Bold ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,262 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PoplarStd.otf', name='Poplar Std', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,785 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSerif-LightItalic.ttf', name='Noto Serif', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-05-30 00:44:50,785 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\javatext.ttf', name='Javanese Text', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,785 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\DejaVuSerifCondensed-Italic.ttf', name='DejaVu Serif', style='italic', variant='normal', weight=400, stretch='condensed', size='scalable')) = 11.25
2025-05-30 00:44:50,785 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RAGE.TTF', name='Rage Italic', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,785 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\POORICH.TTF', name='Poor Richard', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,785 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TrajanPro-Regular.otf', name='Trajan Pro', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,785 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SourceCodePro-Regular.ttf', name='Source Code Pro', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,785 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Carlito-BoldItalic.ttf', name='Carlito', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:50,788 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SCHLBKB.TTF', name='Century Schoolbook', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,788 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\KacstBook.ttf', name='KacstBook', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-05-30 00:44:50,788 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CooperBlackStd-Italic.otf', name='Cooper Std', style='italic', variant='normal', weight=900, stretch='normal', size='scalable')) = 11.525
2025-05-30 00:44:50,789 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Origin.ttf', name='Origin', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,855 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\YuGothL.ttc', name='Yu Gothic', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-05-30 00:44:50,855 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALISTB.TTF', name='Calisto MT', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,857 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\comici.ttf', name='Comic Sans MS', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,858 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\verdanab.ttf', name='Verdana', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,858 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoNaskhArabic-Regular.ttf', name='Noto Naskh Arabic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,858 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguisym.ttf', name='Segoe UI Symbol', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,858 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\comic.ttf', name='Comic Sans MS', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,859 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ChaparralPro-Italic.otf', name='Chaparral Pro', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,859 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LinBiolinum_RI_G.ttf', name='Linux Biolinum G', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,859 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ARIALNBI.TTF', name='Arial', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 1.535
2025-05-30 00:44:50,859 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\timesi.ttf', name='Times New Roman', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,859 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\AdobeArabic-Bold.otf', name='Adobe Arabic', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,860 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GLECB.TTF', name='Gloucester MT Extra Condensed', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-05-30 00:44:50,860 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ERASLGHT.TTF', name='Eras Light ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,860 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msyhbd.ttc', name='Microsoft YaHei', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,861 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguibli.ttf', name='Segoe UI', style='italic', variant='normal', weight=900, stretch='normal', size='scalable')) = 11.525
2025-05-30 00:44:50,861 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\DavidCLM-Medium.ttf', name='David CLM', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-05-30 00:44:50,863 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\KozGoPr6N-Medium.otf', name='Kozuka Gothic Pr6N', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-05-30 00:44:50,863 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSans-Condensed.ttf', name='Noto Sans', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-05-30 00:44:50,864 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\REFSAN.TTF', name='MS Reference Sans Serif', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,864 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\AdobeDevanagari-Regular.otf', name='Adobe Devanagari', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,866 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\comicbd.ttf', name='Comic Sans MS', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,866 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ACaslonPro-Semibold.otf', name='Adobe Caslon Pro', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-05-30 00:44:50,866 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\tahomabd.ttf', name='Tahoma', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,866 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\verdanaz.ttf', name='Verdana', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:50,867 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LSANSI.TTF', name='Lucida Sans', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,867 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\DUBAI-LIGHT.TTF', name='Dubai', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-05-30 00:44:50,867 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ELEPHNT.TTF', name='Elephant', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,868 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\DejaVuSerifCondensed-BoldItalic.ttf', name='DejaVu Serif', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 11.535
2025-05-30 00:44:50,869 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\DejaVuSansMono-Bold.ttf', name='DejaVu Sans Mono', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,869 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\DejaVuSansMono-Oblique.ttf', name='DejaVu Sans Mono', style='oblique', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,870 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RAVIE.TTF', name='Ravie', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,870 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\DejaVuSerifCondensed.ttf', name='DejaVu Serif', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-05-30 00:44:50,873 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\REFSPCL.TTF', name='MS Reference Specialty', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,873 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\YuGothM.ttc', name='Yu Gothic', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-05-30 00:44:50,873 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Carlito-Bold.ttf', name='Carlito', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,875 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SourceSansPro-BlackIt.ttf', name='Source Sans Pro', style='italic', variant='normal', weight=900, stretch='normal', size='scalable')) = 11.525
2025-05-30 00:44:50,875 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GILSANUB.TTF', name='Gill Sans Ultra Bold', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,875 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSerif-CondensedBold.ttf', name='Noto Serif', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-05-30 00:44:50,875 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LFAXDI.TTF', name='Lucida Fax', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-05-30 00:44:50,877 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\calibriz.ttf', name='Calibri', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:50,880 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BELL.TTF', name='Bell MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,880 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\RosewoodStd-Regular.otf', name='Rosewood Std', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,881 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\lucon.ttf', name='Lucida Console', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,882 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LinLibertine_RZI_G.ttf', name='Linux Libertine G', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-05-30 00:44:50,882 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\trebucbi.ttf', name='Trebuchet MS', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:50,882 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MyriadPro-BoldIt.otf', name='Myriad Pro', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:50,883 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ChaparralPro-LightIt.otf', name='Chaparral Pro', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-05-30 00:44:50,883 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simpo.ttf', name='Simplified Arabic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,884 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSans-CondensedBold.ttf', name='Noto Sans', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-05-30 00:44:50,886 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALISTI.TTF', name='Calisto MT', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,886 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BELLB.TTF', name='Bell MT', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,887 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BERNHC.TTF', name='Bernard MT Condensed', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-05-30 00:44:50,887 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\georgiaz.ttf', name='Georgia', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:50,888 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ARIALNI.TTF', name='Arial', style='italic', variant='normal', weight=400, stretch='condensed', size='scalable')) = 1.25
2025-05-30 00:44:50,888 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MyriadHebrew-It.otf', name='Myriad Hebrew', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,889 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_BI.TTF', name='Bodoni MT', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:50,889 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\calibril.ttf', name='Calibri', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-05-30 00:44:50,890 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SourceSansPro-Bold.ttf', name='Source Sans Pro', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,891 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SitkaVF.ttf', name='Sitka', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,891 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GILBI___.TTF', name='Gill Sans MT', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:50,892 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\calibri.ttf', name='Calibri', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,893 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LTYPEO.TTF', name='Lucida Sans Typewriter', style='oblique', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,894 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BAUHS93.TTF', name='Bauhaus 93', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,894 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\tahoma.ttf', name='Tahoma', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,894 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LinLibertine_DR_G.ttf', name='Linux Libertine Display G', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,894 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSans-Italic.ttf', name='Noto Sans', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,896 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simpbdo.ttf', name='Simplified Arabic', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,896 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FELIXTI.TTF', name='Felix Titling', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,896 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MyriadPro-Cond.otf', name='Myriad Pro', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-05-30 00:44:50,896 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FrankRuehlCLM-MediumOblique.ttf', name='Frank Ruehl CLM', style='oblique', variant='normal', weight=500, stretch='normal', size='scalable')) = 11.145
2025-05-30 00:44:50,897 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\DejaVuMathTeXGyre.ttf', name='DejaVu Math TeX Gyre', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,897 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCK.TTF', name='Rockwell', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,897 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOUDOSB.TTF', name='Goudy Old Style', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,897 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALIFI.TTF', name='Californian FB', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,898 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PERTILI.TTF', name='Perpetua Titling MT', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-05-30 00:44:50,898 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\comicz.ttf', name='Comic Sans MS', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:50,898 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SitkaVF-Italic.ttf', name='Sitka', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,899 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ariblk.ttf', name='Arial', style='normal', variant='normal', weight=900, stretch='normal', size='scalable')) = 0.525
2025-05-30 00:44:50,899 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MesquiteStd.otf', name='Mesquite Std', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-05-30 00:44:50,900 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LiberationSansNarrow-Regular.ttf', name='Liberation Sans Narrow', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-05-30 00:44:50,900 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Caladea-BoldItalic.ttf', name='Caladea', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:50,901 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSansGeorgian-Bold.ttf', name='Noto Sans Georgian', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,901 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\DejaVuSerif-Bold.ttf', name='DejaVu Serif', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,901 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\OCRAStd.otf', name='OCR A Std', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,901 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\AdobeGothicStd-Bold.otf', name='Adobe Gothic Std', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,902 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SourceSansPro-SemiboldIt.ttf', name='Source Sans Pro', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-05-30 00:44:50,902 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\bahnschrift.ttf', name='Bahnschrift', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,902 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Alef-Bold.ttf', name='Alef', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,902 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoNaskhArabic-Bold.ttf', name='Noto Naskh Arabic', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,903 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\KozGoPro-Regular.otf', name='Kozuka Gothic Pro', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,903 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LHANDW.TTF', name='Lucida Handwriting', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,903 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCKI.TTF', name='Rockwell', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,903 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GILLUBCD.TTF', name='Gill Sans Ultra Bold Condensed', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-05-30 00:44:50,904 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LinLibertine_RZ_G.ttf', name='Linux Libertine G', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-05-30 00:44:50,904 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Candarai.ttf', name='Candara', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,904 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mmrtextb.ttf', name='Myanmar Text', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,904 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LinBiolinum_RB_G.ttf', name='Linux Biolinum G', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,905 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSerif-Italic.ttf', name='Noto Serif', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,905 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SHOWG.TTF', name='Showcard Gothic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,905 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BKANT.TTF', name='Book Antiqua', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,906 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\trebucbd.ttf', name='Trebuchet MS', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,907 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\KozMinPr6N-Bold.otf', name='Kozuka Mincho Pr6N', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,907 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\constani.ttf', name='Constantia', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,907 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCB_____.TTF', name='Tw Cen MT', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,907 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GARAIT.TTF', name='Garamond', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,908 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSans-Regular.ttf', name='Noto Sans', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,908 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\couri.ttf', name='Courier New', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,908 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NIAGSOL.TTF', name='Niagara Solid', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,908 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\AdobeHebrew-BoldItalic.otf', name='Adobe Hebrew', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-05-30 00:44:50,909 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SourceSansPro-Regular.ttf', name='Source Sans Pro', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,909 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BROADW.TTF', name='Broadway', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,909 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCCB___.TTF', name='Rockwell Condensed', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-05-30 00:44:50,909 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arabtype.ttf', name='Arabic Typesetting', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,909 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SourceCodePro-Medium.ttf', name='Source Code Pro', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-05-30 00:44:50,910 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Rubik-Bold.ttf', name='Rubik', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,910 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msuighub.ttf', name='Microsoft Uighur', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,910 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LiberationSans-BoldItalic.ttf', name='Liberation Sans', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:50,911 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MinionPro-MediumIt.otf', name='Minion Pro', style='italic', variant='normal', weight=500, stretch='normal', size='scalable')) = 11.145
2025-05-30 00:44:50,911 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\DejaVuSansCondensed-Oblique.ttf', name='DejaVu Sans', style='oblique', variant='normal', weight=400, stretch='condensed', size='scalable')) = 11.25
2025-05-30 00:44:50,911 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\COPRGTL.TTF', name='Copperplate Gothic Light', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,912 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguili.ttf', name='Segoe UI', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-05-30 00:44:50,912 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\georgiai.ttf', name='Georgia', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,913 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SNAP____.TTF', name='Snap ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,913 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\KacstOffice.ttf', name='KacstOffice', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-05-30 00:44:50,914 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\StencilStd.otf', name='Stencil Std', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,914 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mmrtext.ttf', name='Myanmar Text', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,914 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\KozMinPr6N-Light.otf', name='Kozuka Mincho Pr6N', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-05-30 00:44:50,914 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\AdobeHeitiStd-Regular.otf', name='Adobe Heiti Std', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,915 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LeelUIsl.ttf', name='Leelawadee UI', style='normal', variant='normal', weight=350, stretch='normal', size='scalable')) = 10.0975
2025-05-30 00:44:50,915 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LTYPEBO.TTF', name='Lucida Sans Typewriter', style='oblique', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-05-30 00:44:50,915 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LiberationSans-Bold.ttf', name='Liberation Sans', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,915 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SourceSerifPro-ExtraLightIt.ttf', name='Source Serif Pro', style='italic', variant='normal', weight=200, stretch='normal', size='scalable')) = 11.24
2025-05-30 00:44:50,916 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\webdings.ttf', name='Webdings', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,916 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Rubik-Italic.ttf', name='Rubik', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,916 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_R.TTF', name='Bodoni MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,916 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MinionPro-BoldIt.otf', name='Minion Pro', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:50,916 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PALSCRI.TTF', name='Palace Script MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,917 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CooperBlackStd.otf', name='Cooper Std', style='normal', variant='normal', weight=900, stretch='normal', size='scalable')) = 10.525
2025-05-30 00:44:50,917 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LTYPE.TTF', name='Lucida Sans Typewriter', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,917 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\corbeli.ttf', name='Corbel', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,917 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_CB.TTF', name='Bodoni MT', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-05-30 00:44:50,918 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\taile.ttf', name='Microsoft Tai Le', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,918 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\trado.ttf', name='Traditional Arabic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,918 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ReemKufi-Regular.ttf', name='Reem Kufi', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,920 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCCB____.TTF', name='Tw Cen MT Condensed', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-05-30 00:44:50,920 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOOKOSBI.TTF', name='Bookman Old Style', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-05-30 00:44:50,920 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\malgunbd.ttf', name='Malgun Gothic', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,921 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BSSYM7.TTF', name='Bookshelf Symbol 7', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,921 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Amiri-Slanted.ttf', name='Amiri', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,921 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\DejaVuSansCondensed-Bold.ttf', name='DejaVu Sans', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-05-30 00:44:50,922 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MinionPro-BoldCn.otf', name='Minion Pro', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-05-30 00:44:50,922 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRABKIT.TTF', name='Franklin Gothic Book', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,922 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FORTE.TTF', name='Forte', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,922 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_BLAR.TTF', name='Bodoni MT', style='normal', variant='normal', weight=900, stretch='normal', size='scalable')) = 10.525
2025-05-30 00:44:50,923 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MiriamCLM-Book.ttf', name='Miriam CLM', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,923 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\opens___.ttf', name='OpenSymbol', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,923 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\KozGoPro-Medium.otf', name='Kozuka Gothic Pro', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-05-30 00:44:50,923 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOTHIC.TTF', name='Century Gothic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,924 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\IMPRISHA.TTF', name='Imprint MT Shadow', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,924 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\tradbdo.ttf', name='Traditional Arabic', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,924 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\AdobeHebrew-Regular.otf', name='Adobe Hebrew', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,925 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SourceCodePro-ExtraLightIt.ttf', name='Source Code Pro', style='italic', variant='normal', weight=200, stretch='normal', size='scalable')) = 11.24
2025-05-30 00:44:50,925 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\AmiriQuran.ttf', name='Amiri Quran', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,926 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BRLNSDB.TTF', name='Berlin Sans FB Demi', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,926 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\constanb.ttf', name='Constantia', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,928 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeuiz.ttf', name='Segoe UI', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:50,928 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ARIALN.TTF', name='Arial', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 0.25
2025-05-30 00:44:50,928 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msjhl.ttc', name='Microsoft JhengHei', style='normal', variant='normal', weight=290, stretch='normal', size='scalable')) = 10.1545
2025-05-30 00:44:50,929 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\majalla.ttf', name='Sakkal Majalla', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,929 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\himalaya.ttf', name='Microsoft Himalaya', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,929 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BirchStd.otf', name='Birch Std', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,929 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\cambriab.ttf', name='Cambria', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,929 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSansArabic-Regular.ttf', name='Noto Sans Arabic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,930 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Candara.ttf', name='Candara', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,930 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NuevaStd-Cond.otf', name='Nueva Std', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-05-30 00:44:50,930 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Scheherazade-Bold.ttf', name='Scheherazade', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,930 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\AGaramondPro-Italic.otf', name='Adobe Garamond Pro', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,930 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALIFB.TTF', name='Californian FB', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,930 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BASKVILL.TTF', name='Baskerville Old Face', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,933 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HoboStd.otf', name='Hobo Std', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-05-30 00:44:50,936 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguisli.ttf', name='Segoe UI', style='italic', variant='normal', weight=350, stretch='normal', size='scalable')) = 11.0975
2025-05-30 00:44:50,936 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSansLisu-Regular.ttf', name='Noto Sans Lisu', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,936 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSerif-CondensedBoldItalic.ttf', name='Noto Serif', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 11.535
2025-05-30 00:44:50,937 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\symbol.ttf', name='Symbol', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,937 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ANTQUAB.TTF', name='Book Antiqua', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,937 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SourceCodePro-MediumIt.ttf', name='Source Code Pro', style='italic', variant='normal', weight=500, stretch='normal', size='scalable')) = 11.145
2025-05-30 00:44:50,937 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msyhl.ttc', name='Microsoft YaHei', style='normal', variant='normal', weight=290, stretch='normal', size='scalable')) = 10.1545
2025-05-30 00:44:50,937 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\KUNSTLER.TTF', name='Kunstler Script', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,938 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\micross.ttf', name='Microsoft Sans Serif', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,938 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SourceCodePro-Semibold.ttf', name='Source Code Pro', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-05-30 00:44:50,938 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSansArmenian-Regular.ttf', name='Noto Sans Armenian', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,938 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\impact.ttf', name='Impact', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,939 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Fences.ttf', name='Fences', style='normal', variant='normal', weight=5, stretch='normal', size='scalable')) = 10.42525
2025-05-30 00:44:50,939 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\COOPBL.TTF', name='Cooper Black', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,939 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NIAGENG.TTF', name='Niagara Engraved', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,941 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\DavidLibre-Regular.ttf', name='David Libre', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,942 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOUDOS.TTF', name='Goudy Old Style', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,942 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoKufiArabic-Regular.ttf', name='Noto Kufi Arabic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,944 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Candaraz.ttf', name='Candara', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:50,944 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\KozGoPr6N-Heavy.otf', name='Kozuka Gothic Pr6N', style='normal', variant='normal', weight=900, stretch='normal', size='scalable')) = 10.525
2025-05-30 00:44:50,944 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRADMIT.TTF', name='Franklin Gothic Demi', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,945 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_CR.TTF', name='Bodoni MT', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-05-30 00:44:50,945 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LiberationSansNarrow-BoldItalic.ttf', name='Liberation Sans Narrow', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 11.535
2025-05-30 00:44:50,945 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\cambriaz.ttf', name='Cambria', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:50,945 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PERTIBD.TTF', name='Perpetua Titling MT', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,945 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Caladea-Italic.ttf', name='Caladea', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,946 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ALGER.TTF', name='Algerian', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,946 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\DejaVuSans-BoldOblique.ttf', name='DejaVu Sans', style='oblique', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:50,946 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALIST.TTF', name='Calisto MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,946 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LetterGothicStd-BoldSlanted.otf', name='Letter Gothic Std', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:50,946 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LSANSDI.TTF', name='Lucida Sans', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-05-30 00:44:50,947 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SourceCodePro-LightIt.ttf', name='Source Code Pro', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-05-30 00:44:50,947 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSans-LightItalic.ttf', name='Noto Sans', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-05-30 00:44:50,948 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOOKOS.TTF', name='Bookman Old Style', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-05-30 00:44:50,948 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Carlito-Italic.ttf', name='Carlito', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,949 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\KozMinPro-Regular.otf', name='Kozuka Mincho Pro', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,949 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Gabriola.ttf', name='Gabriola', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,949 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SourceCodePro-BlackIt.ttf', name='Source Code Pro', style='italic', variant='normal', weight=900, stretch='normal', size='scalable')) = 11.525
2025-05-30 00:44:50,949 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SourceCodePro-Black.ttf', name='Source Code Pro', style='normal', variant='normal', weight=900, stretch='normal', size='scalable')) = 10.525
2025-05-30 00:44:50,950 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCBI____.TTF', name='Tw Cen MT', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:50,950 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LetterGothicStd.otf', name='Letter Gothic Std', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,950 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\AdobeHebrew-Bold.otf', name='Adobe Hebrew', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-05-30 00:44:50,950 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SourceSerifPro-Black.ttf', name='Source Serif Pro', style='normal', variant='normal', weight=900, stretch='normal', size='scalable')) = 10.525
2025-05-30 00:44:50,951 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Amiri-Regular.ttf', name='Amiri', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,951 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRAHVIT.TTF', name='Franklin Gothic Heavy', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,951 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LiberationSerif-Italic.ttf', name='Liberation Serif', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,952 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GenBasBI.ttf', name='Gentium Basic', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:50,952 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SourceSansPro-It.ttf', name='Source Sans Pro', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,952 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SCHLBKBI.TTF', name='Century Schoolbook', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:50,952 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MinionPro-It.otf', name='Minion Pro', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,952 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\DavidLibre-Bold.ttf', name='David Libre', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,953 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\DUBAI-BOLD.TTF', name='Dubai', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,953 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LithosPro-Regular.otf', name='Lithos Pro', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,953 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\palab.ttf', name='Palatino Linotype', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,953 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOTHICBI.TTF', name='Century Gothic', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:50,955 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_CBI.TTF', name='Bodoni MT', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 11.535
2025-05-30 00:44:50,955 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSans-CondensedItalic.ttf', name='Noto Sans', style='italic', variant='normal', weight=400, stretch='condensed', size='scalable')) = 11.25
2025-05-30 00:44:50,956 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\corbelz.ttf', name='Corbel', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:50,956 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRADMCN.TTF', name='Franklin Gothic Demi Cond', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-05-30 00:44:50,956 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TektonPro-BoldCond.otf', name='Tekton Pro', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-05-30 00:44:50,956 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\COPRGTB.TTF', name='Copperplate Gothic Bold', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,957 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NachlieliCLM-Bold.otf', name='Nachlieli CLM', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-05-30 00:44:50,957 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguisbi.ttf', name='Segoe UI', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-05-30 00:44:50,957 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FTLTLT.TTF', name='Footlight MT Light', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-05-30 00:44:50,957 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GIL_____.TTF', name='Gill Sans MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,958 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BrushScriptStd.otf', name='Brush Script Std', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-05-30 00:44:50,958 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRADM.TTF', name='Franklin Gothic Demi', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,958 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MinionPro-Medium.otf', name='Minion Pro', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-05-30 00:44:50,958 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SegUIVar.ttf', name='Segoe UI Variable', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,958 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\AdobeFangsongStd-Regular.otf', name='Adobe Fangsong Std', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,959 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SourceSerifPro-Semibold.ttf', name='Source Serif Pro', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-05-30 00:44:50,959 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguisb.ttf', name='Segoe UI', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-05-30 00:44:50,959 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TektonPro-Bold.otf', name='Tekton Pro', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,959 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSansHebrew-Regular.ttf', name='Noto Sans Hebrew', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,959 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BRITANIC.TTF', name='Britannic Bold', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,959 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Rubik-BoldItalic.ttf', name='Rubik', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:50,959 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\JOKERMAN.TTF', name='Jokerman', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,960 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MTExtra.ttf', name='MT Extra', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,960 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOTHICI.TTF', name='Century Gothic', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,961 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\AdobeMingStd-Light.otf', name='Adobe Ming Std', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-05-30 00:44:50,961 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\DUBAI-REGULAR.TTF', name='Dubai', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,961 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LetterGothicStd-Slanted.otf', name='Letter Gothic Std', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,961 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LiberationMono-Italic.ttf', name='Liberation Mono', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,961 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ENGR.TTF', name='Engravers MT', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-05-30 00:44:50,963 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\KozGoPr6N-Bold.otf', name='Kozuka Gothic Pr6N', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,963 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ANTQUAI.TTF', name='Book Antiqua', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,963 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LinLibertine_RI_G.ttf', name='Linux Libertine G', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,963 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\KozMinPro-Medium.otf', name='Kozuka Mincho Pro', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-05-30 00:44:50,964 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MinionPro-Semibold.otf', name='Minion Pro', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-05-30 00:44:50,964 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FrankRuhlHofshi-Regular.otf', name='Frank Ruhl Hofshi', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,964 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arialbd.ttf', name='Arial', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 0.33499999999999996
2025-05-30 00:44:50,964 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HARLOWSI.TTF', name='Harlow Solid Italic', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,965 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSerif-Regular.ttf', name='Noto Serif', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,965 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\KozMinPr6N-Heavy.otf', name='Kozuka Mincho Pr6N', style='normal', variant='normal', weight=900, stretch='normal', size='scalable')) = 10.525
2025-05-30 00:44:50,965 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LiberationSerif-Regular.ttf', name='Liberation Serif', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,965 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSerifLao-Regular.ttf', name='Noto Serif Lao', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,967 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ntailub.ttf', name='Microsoft New Tai Lue', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,970 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MyriadArabic-It.otf', name='Myriad Arabic', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,970 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\DejaVuSansCondensed-BoldOblique.ttf', name='DejaVu Sans', style='oblique', variant='normal', weight=700, stretch='condensed', size='scalable')) = 11.535
2025-05-30 00:44:50,970 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MiriamMonoCLM-BoldOblique.ttf', name='Miriam Mono CLM', style='oblique', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:50,970 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\calibrib.ttf', name='Calibri', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,971 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\AdobeDevanagari-Italic.otf', name='Adobe Devanagari', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,971 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PLAYBILL.TTF', name='Playbill', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,972 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msjh.ttc', name='Microsoft JhengHei', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,972 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LiberationSerif-Bold.ttf', name='Liberation Serif', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,972 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\DejaVuSans-Bold.ttf', name='DejaVu Sans', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,972 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_PSTC.TTF', name='Bodoni MT', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-05-30 00:44:50,973 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Amiri-BoldSlanted.ttf', name='Amiri', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:50,973 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PER_____.TTF', name='Perpetua', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,973 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HTOWERTI.TTF', name='High Tower Text', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,974 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MTCORSVA.TTF', name='Monotype Corsiva', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,974 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CharlemagneStd-Bold.otf', name='Charlemagne Std', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,974 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSerifHebrew-Bold.ttf', name='Noto Serif Hebrew', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,974 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\majallab.ttf', name='Sakkal Majalla', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,977 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\AdobeNaskh-Medium.otf', name='Adobe Naskh', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-05-30 00:44:50,977 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FrankRuehlCLM-Bold.ttf', name='Frank Ruehl CLM', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,978 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LBRITED.TTF', name='Lucida Bright', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-05-30 00:44:50,978 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguibl.ttf', name='Segoe UI', style='normal', variant='normal', weight=900, stretch='normal', size='scalable')) = 10.525
2025-05-30 00:44:50,978 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CENTAUR.TTF', name='Centaur', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,979 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FrankRuehlCLM-BoldOblique.ttf', name='Frank Ruehl CLM', style='oblique', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:50,979 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoNaskhArabicUI-Regular.ttf', name='Noto Naskh Arabic UI', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,979 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LinBiolinum_R_G.ttf', name='Linux Biolinum G', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,979 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\OLDENGL.TTF', name='Old English Text MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,979 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\courbi.ttf', name='Courier New', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:50,980 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NachlieliCLM-BoldOblique.otf', name='Nachlieli CLM', style='oblique', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-05-30 00:44:50,980 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\OUTLOOK.TTF', name='MS Outlook', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,980 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MinionPro-BoldCnIt.otf', name='Minion Pro', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 11.535
2025-05-30 00:44:50,980 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NuevaStd-BoldCondItalic.otf', name='Nueva Std', style='italic', variant='normal', weight=700, stretch='condensed', size='scalable')) = 11.535
2025-05-30 00:44:50,982 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GiddyupStd.otf', name='Giddyup Std', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,983 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ACaslonPro-Bold.otf', name='Adobe Caslon Pro', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,984 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCKEB.TTF', name='Rockwell Extra Bold', style='normal', variant='normal', weight=800, stretch='normal', size='scalable')) = 10.43
2025-05-30 00:44:50,984 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\WINGDNG2.TTF', name='Wingdings 2', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,984 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TEMPSITC.TTF', name='Tempus Sans ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,984 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoesc.ttf', name='Segoe Script', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,986 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSansLao-Regular.ttf', name='Noto Sans Lao', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:50,986 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\cambriai.ttf', name='Cambria', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:50,987 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NirmalaB.ttf', name='Nirmala UI', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:50,987 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LFAX.TTF', name='Lucida Fax', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,042 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\YuGothR.ttc', name='Yu Gothic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,072 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MiriamCLM-Bold.ttf', name='Miriam CLM', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,082 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\holomdl2.ttf', name='HoloLens MDL2 Assets', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,095 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FrankRuehlCLM-Medium.ttf', name='Frank Ruehl CLM', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-05-30 00:44:51,129 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_B.TTF', name='Bodoni MT', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,131 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GILC____.TTF', name='Gill Sans MT Condensed', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-05-30 00:44:51,134 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeui.ttf', name='Segoe UI', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,153 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ChaparralPro-Bold.otf', name='Chaparral Pro', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,168 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PrestigeEliteStd-Bd.otf', name='Prestige Elite Std', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,171 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\DavidCLM-Bold.ttf', name='David CLM', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,182 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\AdobeFanHeitiStd-Bold.otf', name='Adobe Fan Heiti Std', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-05-30 00:44:51,207 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Carlito-Regular.ttf', name='Carlito', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,210 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ITCBLKAD.TTF', name='Blackadder ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,214 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MiriamMonoCLM-Bold.ttf', name='Miriam Mono CLM', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,216 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\corbelli.ttf', name='Corbel', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-05-30 00:44:51,216 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SourceCodePro-Light.ttf', name='Source Code Pro', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-05-30 00:44:51,220 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\KozMinPro-Bold.otf', name='Kozuka Mincho Pro', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,220 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\WINGDNG3.TTF', name='Wingdings 3', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,221 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\gadugi.ttf', name='Gadugi', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,221 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSans-Light.ttf', name='Noto Sans', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-05-30 00:44:51,222 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TektonPro-BoldObl.otf', name='Tekton Pro', style='oblique', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:51,222 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\OratorStd.otf', name='Orator Std', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,222 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCMI____.TTF', name='Tw Cen MT', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:51,223 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TektonPro-BoldExt.otf', name='Tekton Pro', style='normal', variant='normal', weight=700, stretch='expanded', size='scalable')) = 10.535
2025-05-30 00:44:51,223 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NuevaStd-Italic.otf', name='Nueva Std', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:51,223 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\consola.ttf', name='Consolas', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,224 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BRLNSR.TTF', name='Berlin Sans FB', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,224 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCM_____.TTF', name='Tw Cen MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,226 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoepr.ttf', name='Segoe Print', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,227 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LFAXD.TTF', name='Lucida Fax', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-05-30 00:44:51,227 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msyi.ttf', name='Microsoft Yi Baiti', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,228 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HTOWERT.TTF', name='High Tower Text', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,228 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\AGaramondPro-BoldItalic.otf', name='Adobe Garamond Pro', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:51,229 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\KozGoPro-Bold.otf', name='Kozuka Gothic Pro', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,229 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MyriadArabic-BoldIt.otf', name='Myriad Arabic', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:51,229 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\AdobeKaitiStd-Regular.otf', name='Adobe Kaiti Std', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,230 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GenBasR.ttf', name='Gentium Basic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,230 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\DejaVuSansMono-BoldOblique.ttf', name='DejaVu Sans Mono', style='oblique', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:51,232 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PRISTINA.TTF', name='Pristina', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,234 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguihis.ttf', name='Segoe UI Historic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,234 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ebrima.ttf', name='Ebrima', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,235 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\KozMinPro-Light.otf', name='Kozuka Mincho Pro', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-05-30 00:44:51,236 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MiriamLibre-Regular.otf', name='Miriam Libre', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,236 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\seguiemj.ttf', name='Segoe UI Emoji', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,237 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\AGENCYR.TTF', name='Agency FB', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,237 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSerif-Bold.ttf', name='Noto Serif', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,237 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\aldhabi.ttf', name='Aldhabi', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,238 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_BLAI.TTF', name='Bodoni MT', style='italic', variant='normal', weight=900, stretch='normal', size='scalable')) = 11.525
2025-05-30 00:44:51,238 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MyriadHebrew-Regular.otf', name='Myriad Hebrew', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,238 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PAPYRUS.TTF', name='Papyrus', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,239 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LBRITEI.TTF', name='Lucida Bright', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:51,239 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\monbaiti.ttf', name='Mongolian Baiti', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,241 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ntailu.ttf', name='Microsoft New Tai Lue', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,242 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSans-BoldItalic.ttf', name='Noto Sans', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:51,243 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SegoeIcons.ttf', name='Segoe Fluent Icons', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,243 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSerifGeorgian-Regular.ttf', name='Noto Serif Georgian', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,243 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSerifArmenian-Regular.ttf', name='Noto Serif Armenian', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,243 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeuib.ttf', name='Segoe UI', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,244 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\UrdType.ttf', name='Urdu Typesetting', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,244 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSerifGeorgian-Bold.ttf', name='Noto Serif Georgian', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,244 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FREESCPT.TTF', name='Freestyle Script', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,245 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CENSCBK.TTF', name='Century Schoolbook', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,245 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\palabi.ttf', name='Palatino Linotype', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:51,246 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ariali.ttf', name='Arial', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 1.05
2025-05-30 00:44:51,246 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TrajanPro-Bold.otf', name='Trajan Pro', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,247 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\timesbd.ttf', name='Times New Roman', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,247 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\KozGoPr6N-ExtraLight.otf', name='Kozuka Gothic Pr6N', style='normal', variant='normal', weight=250, stretch='normal', size='scalable')) = 10.1925
2025-05-30 00:44:51,247 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\georgiab.ttf', name='Georgia', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,249 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SourceSerifPro-BoldIt.ttf', name='Source Serif Pro', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:51,249 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arialbi.ttf', name='Arial', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 1.335
2025-05-30 00:44:51,249 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simsun.ttc', name='SimSun', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,249 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\cambria.ttc', name='Cambria', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,249 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\teamviewer15.otf', name='TeamViewer15', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-05-30 00:44:51,250 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LiberationMono-Regular.ttf', name='Liberation Mono', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,250 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Candarab.ttf', name='Candara', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,250 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\KozGoPr6N-Regular.otf', name='Kozuka Gothic Pr6N', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,251 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\courbd.ttf', name='Courier New', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,251 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\YuGothB.ttc', name='Yu Gothic', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,252 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GARA.TTF', name='Garamond', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,255 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LiberationSans-Regular.ttf', name='Liberation Sans', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,255 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ZWAdobeF.TTF', name='ZWAdobeF', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,256 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PERB____.TTF', name='Perpetua', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,256 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MinionPro-SemiboldIt.otf', name='Minion Pro', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-05-30 00:44:51,256 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GIGI.TTF', name='Gigi', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,257 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BlackoakStd.otf', name='Blackoak Std', style='normal', variant='normal', weight=900, stretch='normal', size='scalable')) = 10.525
2025-05-30 00:44:51,257 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\constanz.ttf', name='Constantia', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:51,257 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSansLao-Bold.ttf', name='Noto Sans Lao', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,257 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MATURASC.TTF', name='Matura MT Script Capitals', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,258 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Alef-Regular.ttf', name='Alef', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,258 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MiriamMonoCLM-Book.ttf', name='Miriam Mono CLM', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,258 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SourceSerifPro-SemiboldIt.ttf', name='Source Serif Pro', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-05-30 00:44:51,259 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\palai.ttf', name='Palatino Linotype', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:51,263 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ELEPHNTI.TTF', name='Elephant', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:51,264 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSansArabicUI-Bold.ttf', name='Noto Sans Arabic UI', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,264 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\framd.ttf', name='Franklin Gothic Medium', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,286 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\trebucit.ttf', name='Trebuchet MS', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:51,294 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSerifLao-Bold.ttf', name='Noto Serif Lao', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,300 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCC____.TTF', name='Rockwell Condensed', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-05-30 00:44:51,303 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BRADHITC.TTF', name='Bradley Hand ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,304 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NachlieliCLM-Light.otf', name='Nachlieli CLM', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-05-30 00:44:51,306 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SansSerifCollection.ttf', name='Sans Serif Collection', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,307 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\DejaVuSansCondensed.ttf', name='DejaVu Sans', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-05-30 00:44:51,312 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSerifHebrew-Regular.ttf', name='Noto Serif Hebrew', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,314 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LTYPEB.TTF', name='Lucida Sans Typewriter', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-05-30 00:44:51,314 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SourceSansPro-Light.ttf', name='Source Sans Pro', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-05-30 00:44:51,318 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRAMDCN.TTF', name='Franklin Gothic Medium Cond', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-05-30 00:44:51,319 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MyriadArabic-Bold.otf', name='Myriad Arabic', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,320 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\KozGoPro-ExtraLight.otf', name='Kozuka Gothic Pro', style='normal', variant='normal', weight=250, stretch='normal', size='scalable')) = 10.1925
2025-05-30 00:44:51,320 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\verdanai.ttf', name='Verdana', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:51,320 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\AdobeSongStd-Light.otf', name='Adobe Song Std', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-05-30 00:44:51,321 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SourceSerifPro-LightIt.ttf', name='Source Serif Pro', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-05-30 00:44:51,321 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\DavidCLM-MediumItalic.ttf', name='David CLM', style='italic', variant='normal', weight=500, stretch='normal', size='scalable')) = 11.145
2025-05-30 00:44:51,324 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GILB____.TTF', name='Gill Sans MT', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,324 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSerif-Light.ttf', name='Noto Serif', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-05-30 00:44:51,325 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CENTURY.TTF', name='Century', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,325 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SourceSerifPro-ExtraLight.ttf', name='Source Serif Pro', style='normal', variant='normal', weight=200, stretch='normal', size='scalable')) = 10.24
2025-05-30 00:44:51,326 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BRLNSB.TTF', name='Berlin Sans FB', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,328 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\KozMinPr6N-Regular.otf', name='Kozuka Mincho Pr6N', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,329 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\times.ttf', name='Times New Roman', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,330 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\sylfaen.ttf', name='Sylfaen', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,330 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GenBasB.ttf', name='Gentium Basic', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,332 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VINERITC.TTF', name='Viner Hand ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,332 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\AGENCYB.TTF', name='Agency FB', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,332 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Rubik-Regular.ttf', name='Rubik', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,332 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MyriadHebrew-BoldIt.otf', name='Myriad Hebrew', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:51,334 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOOKOSB.TTF', name='Bookman Old Style', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-05-30 00:44:51,334 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCCM____.TTF', name='Tw Cen MT Condensed', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-05-30 00:44:51,334 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CHILLER.TTF', name='Chiller', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,335 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SourceCodePro-It.ttf', name='Source Code Pro', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:51,335 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\wingding.ttf', name='Wingdings', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,338 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\pala.ttf', name='Palatino Linotype', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,338 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\KozGoPr6N-Light.otf', name='Kozuka Gothic Pr6N', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-05-30 00:44:51,340 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\UrdTypeb.ttf', name='Urdu Typesetting', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,340 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\DejaVuSerif.ttf', name='DejaVu Serif', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,340 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\arial.ttf', name='Arial', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 0.05
2025-05-30 00:44:51,341 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SourceSansPro-ExtraLight.ttf', name='Source Sans Pro', style='normal', variant='normal', weight=200, stretch='normal', size='scalable')) = 10.24
2025-05-30 00:44:51,342 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\TCCEB.TTF', name='Tw Cen MT Condensed Extra Bold', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-05-30 00:44:51,342 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LinLibertine_RB_G.ttf', name='Linux Libertine G', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,342 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\DejaVuSans-ExtraLight.ttf', name='DejaVu Sans', style='normal', variant='normal', weight=200, stretch='normal', size='scalable')) = 10.24
2025-05-30 00:44:51,343 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoescb.ttf', name='Segoe Script', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,346 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRSCRIPT.TTF', name='French Script MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,347 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msuighur.ttf', name='Microsoft Uighur', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,347 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GenBkBasI.ttf', name='Gentium Book Basic', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:51,348 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msjhbd.ttc', name='Microsoft JhengHei', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,348 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Amiri-Bold.ttf', name='Amiri', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,348 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PERBI___.TTF', name='Perpetua', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:51,350 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LiberationSans-Italic.ttf', name='Liberation Sans', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:51,350 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SourceSerifPro-Light.ttf', name='Source Serif Pro', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-05-30 00:44:51,351 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSansHebrew-Bold.ttf', name='Noto Sans Hebrew', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,352 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simsunb.ttf', name='SimSun-ExtB', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,353 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MAGNETOB.TTF', name='Magneto', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,354 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\framdit.ttf', name='Franklin Gothic Medium', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:51,354 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoKufiArabic-Bold.ttf', name='Noto Kufi Arabic', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,355 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALISTBI.TTF', name='Calisto MT', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:51,355 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LSANS.TTF', name='Lucida Sans', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,356 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\georgia.ttf', name='Georgia', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,356 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\KozMinPr6N-ExtraLight.otf', name='Kozuka Mincho Pr6N', style='normal', variant='normal', weight=250, stretch='normal', size='scalable')) = 10.1925
2025-05-30 00:44:51,356 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoMono-Regular.ttf', name='Noto Mono', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,356 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCKB.TTF', name='Rockwell', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,357 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FrankRuhlHofshi-Bold.otf', name='Frank Ruhl Hofshi', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,358 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\KozMinPro-Heavy.otf', name='Kozuka Mincho Pro', style='normal', variant='normal', weight=900, stretch='normal', size='scalable')) = 10.525
2025-05-30 00:44:51,359 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSansArabicUI-Regular.ttf', name='Noto Sans Arabic UI', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,360 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\COLONNA.TTF', name='Colonna MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,360 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Inkfree.ttf', name='Ink Free', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,361 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ARLRDBD.TTF', name='Arial Rounded MT Bold', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,363 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NirmalaS.ttf', name='Nirmala UI', style='normal', variant='normal', weight=350, stretch='normal', size='scalable')) = 10.0975
2025-05-30 00:44:51,363 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\DejaVuSerif-Italic.ttf', name='DejaVu Serif', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:51,363 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SourceSansPro-Semibold.ttf', name='Source Sans Pro', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-05-30 00:44:51,364 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeprb.ttf', name='Segoe Print', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,364 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MOD20.TTF', name='Modern No. 20', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,364 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Caladea-Bold.ttf', name='Caladea', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,364 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LBRITE.TTF', name='Lucida Bright', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,365 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MyriadPro-Semibold.otf', name='Myriad Pro', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-05-30 00:44:51,366 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GLSNECB.TTF', name='Gill Sans MT Ext Condensed Bold', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-05-30 00:44:51,367 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_I.TTF', name='Bodoni MT', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:51,367 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LiberationMono-BoldItalic.ttf', name='Liberation Mono', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:51,369 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VLADIMIR.TTF', name='Vladimir Script', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,369 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\corbell.ttf', name='Corbel', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-05-30 00:44:51,369 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BRUSHSCI.TTF', name='Brush Script MT', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:51,369 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeuisl.ttf', name='Segoe UI', style='normal', variant='normal', weight=350, stretch='normal', size='scalable')) = 10.0975
2025-05-30 00:44:51,370 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\AdobeArabic-Italic.otf', name='Adobe Arabic', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:51,370 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BELLI.TTF', name='Bell MT', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:51,372 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\KozGoPro-Light.otf', name='Kozuka Gothic Pro', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-05-30 00:44:51,373 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\gadugib.ttf', name='Gadugi', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,374 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LBRITEDI.TTF', name='Lucida Bright', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-05-30 00:44:51,374 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\INFROMAN.TTF', name='Informal Roman', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,375 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MiriamMonoCLM-BookOblique.ttf', name='Miriam Mono CLM', style='oblique', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:51,375 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LinLibertine_RBI_G.ttf', name='Linux Libertine G', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:51,375 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MyriadHebrew-Bold.otf', name='Myriad Hebrew', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,376 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\KozMinPr6N-Medium.otf', name='Kozuka Mincho Pr6N', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-05-30 00:44:51,376 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\mingliub.ttc', name='MingLiU-ExtB', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,376 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\OCRAEXT.TTF', name='OCR A Extended', style='normal', variant='normal', weight=400, stretch='expanded', size='scalable')) = 10.25
2025-05-30 00:44:51,376 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PERI____.TTF', name='Perpetua', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:51,377 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SourceSerifPro-Bold.ttf', name='Source Serif Pro', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,377 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ACaslonPro-SemiboldItalic.otf', name='Adobe Caslon Pro', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-05-30 00:44:51,379 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MAIAN.TTF', name='Maiandra GD', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,380 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GenBkBasBI.ttf', name='Gentium Book Basic', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:51,380 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ANTQUABI.TTF', name='Book Antiqua', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:51,381 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\AGaramondPro-Bold.otf', name='Adobe Garamond Pro', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,381 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ACaslonPro-BoldItalic.otf', name='Adobe Caslon Pro', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:51,382 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GenBkBasR.ttf', name='Gentium Book Basic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,382 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\phagspab.ttf', name='Microsoft PhagsPa', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,383 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CALIFR.TTF', name='Californian FB', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,383 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LiberationMono-Bold.ttf', name='Liberation Mono', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,383 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ACaslonPro-Regular.otf', name='Adobe Caslon Pro', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,383 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\KozGoPro-Heavy.otf', name='Kozuka Gothic Pro', style='normal', variant='normal', weight=900, stretch='normal', size='scalable')) = 10.525
2025-05-30 00:44:51,384 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\STENCIL.TTF', name='Stencil', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,384 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOOKOSI.TTF', name='Bookman Old Style', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-05-30 00:44:51,387 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\constan.ttf', name='Constantia', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,388 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\VIVALDII.TTF', name='Vivaldi', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:51,388 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NuevaStd-BoldCond.otf', name='Nueva Std', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-05-30 00:44:51,389 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\corbel.ttf', name='Corbel', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,389 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\JUICE___.TTF', name='Juice ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,389 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\timesbi.ttf', name='Times New Roman', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:51,389 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\trebuc.ttf', name='Trebuchet MS', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,390 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSansArmenian-Bold.ttf', name='Noto Sans Armenian', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,390 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HATTEN.TTF', name='Haettenschweiler', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,390 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\malgunsl.ttf', name='Malgun Gothic', style='normal', variant='normal', weight=300, stretch='normal', size='scalable')) = 10.145
2025-05-30 00:44:51,392 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\consolab.ttf', name='Consolas', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,397 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ERASDEMI.TTF', name='Eras Demi ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,398 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Candarali.ttf', name='Candara', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:51,398 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ITCEDSCR.TTF', name='Edwardian Script ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,399 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOTHICB.TTF', name='Century Gothic', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,401 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msyh.ttc', name='Microsoft YaHei', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,402 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOUDOSI.TTF', name='Goudy Old Style', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:51,403 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GILI____.TTF', name='Gill Sans MT', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:51,403 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\BOD_CI.TTF', name='Bodoni MT', style='italic', variant='normal', weight=400, stretch='condensed', size='scalable')) = 11.25
2025-05-30 00:44:51,404 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\corbelb.ttf', name='Corbel', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,404 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LATINWD.TTF', name='Wide Latin', style='normal', variant='normal', weight=400, stretch='expanded', size='scalable')) = 10.25
2025-05-30 00:44:51,405 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SourceSerifPro-It.ttf', name='Source Serif Pro', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:51,405 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\OratorStd-Slanted.otf', name='Orator Std', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:51,406 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Candaral.ttf', name='Candara', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,408 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SourceSerifPro-BlackIt.ttf', name='Source Serif Pro', style='italic', variant='normal', weight=900, stretch='normal', size='scalable')) = 11.525
2025-05-30 00:44:51,408 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSansArabic-Bold.ttf', name='Noto Sans Arabic', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,410 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoNaskhArabicUI-Bold.ttf', name='Noto Naskh Arabic UI', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,411 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\DejaVuSerifCondensed-Bold.ttf', name='DejaVu Serif', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-05-30 00:44:51,411 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ChaparralPro-Regular.otf', name='Chaparral Pro', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,411 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\calibrii.ttf', name='Calibri', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:51,412 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\AdobeArabic-BoldItalic.otf', name='Adobe Arabic', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:51,416 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\PARCHM.TTF', name='Parchment', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,417 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LCALLIG.TTF', name='Lucida Calligraphy', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:51,418 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\AGaramondPro-Regular.otf', name='Adobe Garamond Pro', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,419 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SCRIPTBL.TTF', name='Script MT Bold', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,422 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CASTELAR.TTF', name='Castellar', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,423 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MISTRAL.TTF', name='Mistral', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,424 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\AdobeHebrew-Italic.otf', name='Adobe Hebrew', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:51,424 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\KozMinPro-ExtraLight.otf', name='Kozuka Mincho Pro', style='normal', variant='normal', weight=250, stretch='normal', size='scalable')) = 10.1925
2025-05-30 00:44:51,425 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ITCKRIST.TTF', name='Kristen ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,427 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSerif-BoldItalic.ttf', name='Noto Serif', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:51,429 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SourceSansPro-BoldIt.ttf', name='Source Sans Pro', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:51,430 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SourceSansPro-LightIt.ttf', name='Source Sans Pro', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-05-30 00:44:51,432 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\DejaVuSerif-BoldItalic.ttf', name='DejaVu Serif', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:51,432 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\FRABK.TTF', name='Franklin Gothic Book', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,435 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MiriamLibre-Bold.otf', name='Miriam Libre', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,436 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segoeuii.ttf', name='Segoe UI', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:51,437 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\taileb.ttf', name='Microsoft Tai Le', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,437 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ONYX.TTF', name='Onyx', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,438 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSans-Bold.ttf', name='Noto Sans', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,439 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SourceSerifPro-Regular.ttf', name='Source Serif Pro', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,439 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LiberationSansNarrow-Bold.ttf', name='Liberation Sans Narrow', style='normal', variant='normal', weight=700, stretch='condensed', size='scalable')) = 10.535
2025-05-30 00:44:51,445 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\DejaVuSans.ttf', name='DejaVu Sans', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,446 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LiberationSerif-BoldItalic.ttf', name='Liberation Serif', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:51,447 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LiberationSansNarrow-Italic.ttf', name='Liberation Sans Narrow', style='italic', variant='normal', weight=400, stretch='condensed', size='scalable')) = 11.25
2025-05-30 00:44:51,452 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\CURLZ___.TTF', name='Curlz MT', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,464 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NotoSerif-Condensed.ttf', name='Noto Serif', style='normal', variant='normal', weight=400, stretch='condensed', size='scalable')) = 10.25
2025-05-30 00:44:51,473 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\Scheherazade-Regular.ttf', name='Scheherazade', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,473 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\calibrili.ttf', name='Calibri', style='italic', variant='normal', weight=300, stretch='normal', size='scalable')) = 11.145
2025-05-30 00:44:51,474 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\cour.ttf', name='Courier New', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,474 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\NuevaStd-Bold.otf', name='Nueva Std', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,478 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\consolai.ttf', name='Consolas', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2025-05-30 00:44:51,480 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\msgothic.ttc', name='MS Gothic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,480 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\AdobeDevanagari-Bold.otf', name='Adobe Devanagari', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,484 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LSANSD.TTF', name='Lucida Sans', style='normal', variant='normal', weight=600, stretch='normal', size='scalable')) = 10.24
2025-05-30 00:44:51,484 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ChaparralPro-BoldIt.otf', name='Chaparral Pro', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:51,485 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MyriadPro-Regular.otf', name='Myriad Pro', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,487 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LithosPro-Black.otf', name='Lithos Pro', style='normal', variant='normal', weight=850, stretch='normal', size='scalable')) = 10.4775
2025-05-30 00:44:51,487 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LeelaUIb.ttf', name='Leelawadee UI', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,491 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GARABD.TTF', name='Garamond', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,491 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\DejaVuSansMono.ttf', name='DejaVu Sans Mono', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,493 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\segmdl2.ttf', name='Segoe MDL2 Assets', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,494 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MinionPro-Regular.otf', name='Minion Pro', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,494 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\GOUDYSTO.TTF', name='Goudy Stout', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,494 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ERASMD.TTF', name='Eras Medium ITC', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,495 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\ROCKBI.TTF', name='Rockwell', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:51,495 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SourceCodePro-SemiboldIt.ttf', name='Source Code Pro', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-05-30 00:44:51,495 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\DUBAI-MEDIUM.TTF', name='Dubai', style='normal', variant='normal', weight=500, stretch='normal', size='scalable')) = 10.145
2025-05-30 00:44:51,495 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\AdobeDevanagari-BoldItalic.otf', name='Adobe Devanagari', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:51,498 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\HARNGTON.TTF', name='Harrington', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,499 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SourceCodePro-BoldIt.ttf', name='Source Code Pro', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:51,499 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\AdobeArabic-Regular.otf', name='Adobe Arabic', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,500 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\MyriadPro-SemiboldIt.otf', name='Myriad Pro', style='italic', variant='normal', weight=600, stretch='normal', size='scalable')) = 11.24
2025-05-30 00:44:51,501 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\DavidCLM-BoldItalic.ttf', name='David CLM', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:51,501 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\consolaz.ttf', name='Consolas', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2025-05-30 00:44:51,501 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SourceCodePro-Bold.ttf', name='Source Code Pro', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2025-05-30 00:44:51,502 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\LinLibertine_R_G.ttf', name='Linux Libertine G', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,502 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\simpfxo.ttf', name='Simplified Arabic Fixed', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2025-05-30 00:44:51,502 - matplotlib.font_manager - DEBUG - findfont: score(FontEntry(fname='C:\\Windows\\Fonts\\SourceSansPro-ExtraLightIt.ttf', name='Source Sans Pro', style='italic', variant='normal', weight=200, stretch='normal', size='scalable')) = 11.24
2025-05-30 00:44:51,502 - matplotlib.font_manager - DEBUG - findfont: Matching Arial:style=normal:variant=normal:weight=normal:stretch=normal:size=10.0 to Arial ('C:\\Windows\\Fonts\\arial.ttf') with score of 0.050000.
2025-05-30 00:44:51,504 - __main__ - DEBUG - Found font 'Arial' (or fallback) at: C:\Windows\Fonts\arial.ttf
2025-05-30 00:44:51,515 - __main__ - INFO - Successfully loaded font: Arial from C:\Windows\Fonts\arial.ttf
2025-05-30 00:44:51,515 - __main__ - INFO - Using font: Arial
2025-05-30 00:44:51,515 - __main__ - INFO - Initializing GrainAnalysisApp...
2025-05-30 00:44:51,520 - __main__ - INFO - Loaded theme preference: light
2025-05-30 00:44:51,596 - __main__ - WARNING - Icon not found or invalid for mode 'selection': icons/select.png
2025-05-30 00:44:51,597 - __main__ - WARNING - Icon not found or invalid for mode 'scale': icons/scale.png
2025-05-30 00:44:51,598 - __main__ - WARNING - Icon not found or invalid for mode 'pan': icons/pan.png
2025-05-30 00:44:51,599 - __main__ - DEBUG - Interaction mode set to: selection
2025-05-30 00:44:51,634 - __main__ - DEBUG - Action states updated. Processing: False, Model Loaded: False, Image Loaded: False, Annotations Exist: False, Scale Set: False, Results Exist: False, Selected Grains: 0
2025-05-30 00:44:51,636 - __main__ - INFO - Status: Ready. Load an image to start.
2025-05-30 00:44:51,638 - __main__ - DEBUG - Action states updated. Processing: False, Model Loaded: False, Image Loaded: False, Annotations Exist: False, Scale Set: False, Results Exist: False, Selected Grains: 0
2025-05-30 00:44:51,638 - __main__ - INFO - Using device: cpu
2025-05-30 00:44:51,638 - __main__ - INFO - GrainAnalysisApp initialization complete.
2025-05-30 00:44:51,638 - __main__ - INFO - Status: Loading model...
2025-05-30 00:44:51,638 - __main__ - DEBUG - Action states updated. Processing: False, Model Loaded: False, Image Loaded: False, Annotations Exist: False, Scale Set: False, Results Exist: False, Selected Grains: 0
2025-05-30 00:44:51,714 - __main__ - INFO - Loading FastSAM model from: D:\Github\grainsight_v5\src\grainsight_components\models\FastSAM-x.pt
2025-05-30 00:44:52,876 - __main__ - INFO - FastSAM model loaded successfully (on CPU).
2025-05-30 00:44:52,881 - __main__ - INFO - Model loaded successfully. Ready for processing.
2025-05-30 00:44:52,882 - __main__ - INFO - Status: Model loaded. Ready.
2025-05-30 00:44:52,883 - __main__ - DEBUG - Action states updated. Processing: False, Model Loaded: True, Image Loaded: False, Annotations Exist: False, Scale Set: False, Results Exist: False, Selected Grains: 0
