@echo off
echo GrainSight Executable Builder
echo =============================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.12 or later and try again
    pause
    exit /b 1
)

echo Step 1: Installing build dependencies...
echo.
python -m pip install --upgrade pip
python -m pip install -r requirements_build.txt
if errorlevel 1 (
    echo Error: Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo Step 2: Creating application icon...
echo.
python create_icon.py
if errorlevel 1 (
    echo Warning: Icon creation failed, continuing without custom icon
)

echo.
echo Step 3: Building executable...
echo.
python build_exe.py
if errorlevel 1 (
    echo Error: Build failed
    pause
    exit /b 1
)

echo.
echo =============================
echo Build completed successfully!
echo =============================
echo.
echo The executable and installer are in the 'dist' folder:
echo - GrainSight.exe (main application)
echo - install.bat (installation script)
echo - README.txt (user documentation)
echo.
echo To distribute:
echo 1. Zip the entire 'dist' folder
echo 2. Users can run install.bat for system installation
echo 3. Or run GrainSight.exe directly for portable usage
echo.
pause
