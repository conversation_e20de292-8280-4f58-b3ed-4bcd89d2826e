# -*- mode: python ; coding: utf-8 -*-
import sys
import os
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# Collect all data files and submodules
datas = []
hiddenimports = []

# Add source code as data files
datas += [('src', 'src')]

# Add model files if they exist
if os.path.exists('src/grainsight_components/models'):
    datas += [('src/grainsight_components/models', 'src/grainsight_components/models')]

# Add icon files and assets
if os.path.exists('src/grainsight_components/gui/assets'):
    datas += [('src/grainsight_components/gui/assets', 'src/grainsight_components/gui/assets')]

# Add any additional resource directories
if os.path.exists('icons'):
    datas += [('icons', 'icons')]

# Collect PyTorch data files
try:
    datas += collect_data_files('torch')
    datas += collect_data_files('torchvision')
except:
    pass

# Collect Ultralytics data files
try:
    datas += collect_data_files('ultralytics')
except:
    pass

# Collect timm data files
try:
    datas += collect_data_files('timm')
except:
    pass

# Collect PySide6 data files
try:
    datas += collect_data_files('PySide6')
except:
    pass

# Hidden imports for all the modules that might not be detected
hiddenimports += [
    # Core Python modules
    'logging',
    'json',
    'csv',
    'sqlite3',
    
    # Scientific computing
    'numpy',
    'pandas',
    'matplotlib',
    'matplotlib.backends.backend_qt5agg',
    'matplotlib.backends.backend_qtagg',
    'PIL',
    'PIL.Image',
    'PIL.ImageTk',
    'cv2',
    'scipy',
    'scipy.spatial',
    'scipy.spatial.distance',
    
    # PyTorch and related
    'torch',
    'torchvision',
    'torchvision.transforms',
    'timm',
    'timm.models',
    'timm.layers',
    
    # Ultralytics/YOLO
    'ultralytics',
    'ultralytics.models',
    'ultralytics.models.yolo',
    'ultralytics.utils',
    
    # PySide6/Qt
    'PySide6',
    'PySide6.QtCore',
    'PySide6.QtGui',
    'PySide6.QtWidgets',
    'PySide6.QtOpenGL',
    
    # Application modules
    'src',
    'src.grainsight_components',
    'src.grainsight_components.core',
    'src.grainsight_components.core.segmentation',
    'src.grainsight_components.core.analysis',
    'src.grainsight_components.core.patching',
    'src.grainsight_components.core.patch_refinement',
    'src.grainsight_components.core.image_utils',
    'src.grainsight_components.gui',
    'src.grainsight_components.gui.main_window',
    'src.grainsight_components.gui.workers',
    'src.grainsight_components.gui.widgets',
    'src.grainsight_components.gui.dialogs',
    'src.grainsight_components.gui.utils',
    'src.grainsight_components.mobile_sam',
    'src.grainsight_components.mobile_sam.predictor',
    'src.grainsight_components.mobile_sam.build_sam',
    'src.grainsight_components.mobile_sam.automatic_mask_generator',
]

# Try to collect all submodules automatically
try:
    hiddenimports += collect_submodules('src')
    hiddenimports += collect_submodules('ultralytics')
    hiddenimports += collect_submodules('timm')
except:
    pass

# Remove duplicates
hiddenimports = list(set(hiddenimports))

block_cipher = None

a = Analysis(
    ['grainsight.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # Exclude unnecessary modules to reduce size
        'tkinter',
        'test',
        'unittest',
        'pdb',
        'doctest',
        'difflib',
        'inspect',
        'pydoc',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='GrainSight',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # Set to True for debugging, False for release
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='src/grainsight_components/gui/assets/icons/grain_icon.ico' if os.path.exists('src/grainsight_components/gui/assets/icons/grain_icon.ico') else None,
)
