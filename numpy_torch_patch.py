import torch
import numpy as np

# Define a custom function to safely convert NumPy arrays to PyTorch tensors
def safe_from_numpy(numpy_array):
    """Safely convert a NumPy array to a PyTorch tensor, handling the NumPy initialization issue."""
    try:
        # Try the standard conversion first
        return torch.from_numpy(numpy_array)
    except Exception as e:
        # Fallback: convert to list first, then to tensor
        try:
            # Convert to Python list first
            array_list = numpy_array.tolist()
            # Create tensor from list
            tensor = torch.tensor(array_list)
            
            # Try to preserve dtype if possible
            numpy_dtype = str(numpy_array.dtype)
            if 'float' in numpy_dtype:
                if '64' in numpy_dtype:
                    tensor = tensor.to(torch.float64)
                elif '32' in numpy_dtype:
                    tensor = tensor.to(torch.float32)
                else:
                    tensor = tensor.to(torch.float)
            elif 'int' in numpy_dtype:
                if '64' in numpy_dtype:
                    tensor = tensor.to(torch.int64)
                elif '32' in numpy_dtype:
                    tensor = tensor.to(torch.int32)
                elif '16' in numpy_dtype:
                    tensor = tensor.to(torch.int16)
                elif '8' in numpy_dtype:
                    tensor = tensor.to(torch.int8)
                else:
                    tensor = tensor.to(torch.int)
            return tensor
        except Exception as inner_e:
            print(f"Inner conversion error: {inner_e}")
            # Last resort: try direct tensor creation
            return torch.tensor(numpy_array.astype(float).tolist())

print("Testing the safe_from_numpy function:")
try:
    # Create a NumPy array
    a = np.array([1, 2, 3])
    print(f"NumPy array: {a}")
    
    # Convert to PyTorch tensor using our safe function
    b = safe_from_numpy(a)
    print(f"PyTorch tensor: {b}")
    
    # Test with float array
    c = np.array([1.1, 2.2, 3.3])
    print(f"NumPy float array: {c}")
    d = safe_from_numpy(c)
    print(f"PyTorch float tensor: {d}")
    
    print("Conversion successful!")
except Exception as e:
    print(f"Error: {e}")